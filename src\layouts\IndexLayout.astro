---
import Layout from '~/layouts/PageLayout.astro';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { services, settings, properties } from '~/connectors';

// Components
import Note from '~/components/widgets/Note.astro';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';
import BlogLatestPosts from '~/components/widgets/BlogLatestPosts.astro';
import ServiceWidget from '~/components/services/ServiceWidget.astro';
import Agendas from '~/components/ui/Agendas.astro';
import PropertiesShowcase from '~/components/real-estate/PropertiesShowcase.astro';

// Utils & Constants
const SERVICES_ENABLED_KEY = 'services.isEnabled';
const HOUSING_ENABLED_KEY = 'housing.isEnabled';

// Page Setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

// Feature Flags
const hasServices = services.length > 0;
const hasProperties = !!properties.length;
const servicesIsEnabled = settings.find((s) => s.key === SERVICES_ENABLED_KEY)?.value;
const housingIsEnabled = settings.find((s) => s.key === HOUSING_ENABLED_KEY)?.value;

const metadata = {
  title: t('indexPage.metadata.title'),
  description: t('indexPage.metadata.description'),
  ignoreTitleTemplate: true,
};

// Section Configs
const sectionsConfig = {
  properties: {
    id: 'properties-section',
    // title: 'real-estate',
    title: t('real-estate'),
    information: 'Hand-Picked & Verified Offers',
    linkText: '🏡 Rent or Buy',
    linkUrl: translatePath('/real-estate'),
  },
  services: {
    id: 'services-slider',
    title: t('services'),
    information: t('services.information'),
    linkText: `👉 ${t('viewAll')}`,
    linkUrl: translatePath('/services'),
  },
  agendas: {
    title: t('upcoming.events'),
  },
};
---

<Layout metadata={metadata}>
  <Note>{t('indexPage.metadata.note')}</Note>

  <BlogLatestPosts id="blog" title={t('latestContents')} />

  <!-- {hasServices && servicesIsEnabled && <ServiceWidget {...sectionsConfig.services} addScript />} -->

  <FollowSectionSocials section="livegc" ctaText={t('followUs')} />

  <Agendas {...sectionsConfig.agendas} />

  {
    hasProperties && housingIsEnabled && (
      <PropertiesShowcase properties={properties} {...sectionsConfig.properties} addScript>
        <div class="absolute inset-0 bg-blue-50 dark:bg-transparent" slot="bg" />
      </PropertiesShowcase>
    )
  }
</Layout>