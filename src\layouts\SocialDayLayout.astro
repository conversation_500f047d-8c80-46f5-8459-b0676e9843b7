---
import EventDayLayout from '~/layouts/EventDayLayout.astro';
import { socialEvents } from '~/connectors/nocodb';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
// import SocialCustomAlert from '~/components/social/SocialCustomAlert.astro';
// import SocialSponsors from '~/components/social/SocialSponsors.astro';

export interface Props {
  dayOfTheWeek: number;
}

const { dayOfTheWeek } = Astro.props;

// i18n setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

// Data fetching
const event = socialEvents.find((e) => e.day_of_the_week === dayOfTheWeek);
if (!event) {
  throw new Error(`No event found for day ${dayOfTheWeek}`);
}

// Determina se mostrare sponsor per questo evento
const showSponsors = event.has_sponsors === true;
---

<EventDayLayout
  dayOfTheWeek={dayOfTheWeek}
  event={event}
  eventType="socialization"
  lang={lang}
  t={t}
  translatePath={translatePath}
  url={Astro.url}
  options={{
    // Disabilita la sezione di call to action in fondo
    disableCallToAction: true,
    // Indica che useremo un avviso personalizzato quando non ci sono eventi
    noEventsAlertCustom: true,
  }}
>
  <!-- Avviso personalizzato quando non ci sono eventi -->
  <!-- <SocialCustomAlert slot="custom-no-events-alert" /> -->

  <!-- Aggiungi gli sponsor se necessario -->
  <!-- {showSponsors && <SocialSponsors event={event} slot="before-footer" />} -->
</EventDayLayout>
