---
import Layout from '~/layouts/PageLayout.astro';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import FollowSectionSocials from '~/components/common/FollowSectionSocials.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import TopNavLinks from '~/components/ui/TopNavLinks.astro';
import Note from '~/components/widgets/Note.astro';
import DisqusComment from '~/components/common/DisqusComment.astro';
// import { danceSchools } from '~/connectors/nocodb';
// import Icon from 'node_modules/astro-icon/components/Icon.astro';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const metadata = {
  title: t('danceSchoolsPage.metadata.title'),
  description: t('danceSchoolsPage.metadata.description'),
  ignoreTitleTemplate: true,
};
---

<Layout metadata={metadata}>
  <!-- https://merakiui.com/components/application-ui/cards -->
  <!-- {
    danceSchools.map((school) => (
      <>
        <div class="w-full max-w-sm overflow-hidden bg-white rounded-lg shadow-lg dark:bg-gray-800 my-4">
          <div class="flex justify-center px-6 py-3 ">
            <div
              class="bg-cover bg-center w-10 h-10 rounded-full mr-3 border-2 border-black p-1"
              style={`background-image: url('${school?.logo?.length ? school?.logo[0].url : ''}')`}
            />
            <div class="flex py-3 justify-center">
              <p class="font-bold">{school.name}</p>
            </div>
          </div>

          <div class="">
            {school?.locations_names?.map((location, i) => (
              // <a href={school.link_maps[i] || '#'} target="_blank" class="mb-4">
              //   <Icon name="tabler:map-pin" width={20} height={20} class="inline-block -mt-0.5 mr-2" />
              //   {location ?? `<strong>${location}</strong> -`}
              //   {school?.addresses[i] ?? school?.addresses[i]}
              // </a>
              <>
                <Fragment set:html={school?.iframe_maps[i] || ''} />{' '}
              </>
            ))}
          </div>

          <div class="py-3 flex justify-center">
            <div class="flex">
              {school?.web && (
                <a href={school?.web} target="_blank" class="rtl:ml-0 rtl:mr-2">
                  <Icon
                    name="tabler:home"
                    class="w-6 h-6 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
                  />
                </a>
              )}

              {school?.contact_number && (
                <a href={`tel:${school?.contact_number}`} target="_blank" class="ml-2 rtl:ml-0 rtl:mr-2">
                  <Icon
                    name="tabler:phone"
                    class="w-6 h-6 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
                  />
                </a>
              )}

              {school?.contact_number && (
                <a href={`https://wa.me/${school?.contact_number}`} target="_blank" class="ml-2 rtl:ml-0 rtl:mr-2">
                  <Icon
                    name="tabler:brand-whatsapp"
                    class="w-6 h-6 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
                  />
                </a>
              )}

              {school?.email && (
                <a href={`mailto:${school?.email}`} target="_blank" class="ml-2 rtl:ml-0 rtl:mr-2">
                  <Icon
                    name="tabler:mail"
                    class="w-6 h-6 text-gray-400 dark:text-slate-500 hover:text-black dark:hover:text-slate-300"
                  />
                </a>
              )}
            </div>
          </div>
        </div>
      </>
    ))
  } -->

  <Note><h1 class="uppercase">{t('danceSchoolsPage.metadata.title')}</h1></Note>

  <iframe
    src="https://www.google.com/maps/d/u/1/embed?mid=1hm0gLeIGNmZjA0laDXvYhhSAsLOunZ8&ehbc=2E312F&noprof=1"
    width="100%"
    height="600"></iframe>

  <section class="py-8 sm:py-16 lg:py-20 mx-auto">
    <div class="text-muted text-xs mx-auto max-w-3xl px-6 sm:px-6">
      <div class="mb-8">
        <TopNavLinks
          leftHref={translatePath('/dancing')}
          leftText={t('latinDancingDayPage.backBtn')}
          leftVisible={true}
          rightHref={translatePath('/dancing')}
          rightText={t('latinDancingDayPage.nextEventBtn')}
          rightVisible={false}
        />
      </div>
    </div>

    <div class="text-muted text-xs mx-auto max-w-3xl px-6 sm:px-6">
      <!-- <DisqusComment /> -->
    </div>
  </section>

  <FollowSectionSocials section="dancing" ctaText={t('followUs')} />

  <CallToAction
    title={t('danceSchoolsPage.callToAction.title')}
    subtitle={t('danceSchoolsPage.callToAction.subtitle')}
    callToAction={{
      text: t('dancing.callToAction.ctaText'),
      href: translatePath('/contact'),
    }}
  />
</Layout>
