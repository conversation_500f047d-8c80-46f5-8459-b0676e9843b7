import { getImage } from 'astro:assets';
import { uploadImageAndGetUrl } from './pulpo.js';
import { createSocialImageWithText } from '../utils/imagesUtils.js';

const NOCODB_BASE_URL = import.meta.env.NOCODB_BASE_URL;
const NOCODB_API_TOKEN = import.meta.env.NOCODB_API_TOKEN;
const NOCODB_BASE_ID = import.meta.env.NOCODB_BASE_ID;

const TABLE_NAMES = [
    "social_agenda",
    "dancing_agenda",
    "reviews",
    "vocabulary",
    "bio",
    "services",
    "housing",
    "settings",
];

// Cache for table metadata to avoid repeated API calls
let tableMetadataCache = null;

async function fetchTableMetadata() {
    if (tableMetadataCache) {
        return tableMetadataCache;
    }

    const url = `${NOCODB_BASE_URL}api/v2/meta/bases/${NOCODB_BASE_ID}/tables`;
    const response = await fetch(url, {
        headers: {
            'xc-token': NOCODB_API_TOKEN,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch table metadata: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    tableMetadataCache = data.list || data;
    return tableMetadataCache;
}

function getTableIdByLabel(tables, label) {
    const table = tables.find(t => t.title?.toLowerCase() === label.toLowerCase());
    return table?.id || null;
}

// ===== DATA TRANSFORMATION FUNCTIONS =====

/**
 * Transform NocoDB attachment to Airtable format
 */
function transformAttachment(nocoAttachment) {
    if (!nocoAttachment || typeof nocoAttachment !== 'object') {
        return null;
    }

    // Priority: signedPath (temporary) > url (if present) > path (permanent)
    let url = nocoAttachment.signedPath || nocoAttachment.url || nocoAttachment.path;

    // If it's a relative path, make it absolute using NOCODB_BASE_URL
    if (url && !url.startsWith('http')) {
        url = `${NOCODB_BASE_URL}${url}`;
    }

    if (!url) {
        console.warn('Attachment without valid URL:', nocoAttachment);
        return null;
    }

    // Transform thumbnails to Airtable format
    const thumbnails = {};
    if (nocoAttachment.thumbnails) {
        Object.entries(nocoAttachment.thumbnails).forEach(([size, thumb]) => {
            if (thumb && thumb.signedPath) {
                let thumbUrl = thumb.signedPath;
                if (!thumbUrl.startsWith('http')) {
                    thumbUrl = `${NOCODB_BASE_URL}${thumbUrl}`;
                }
                thumbnails[size] = {
                    url: thumbUrl,
                    width: thumb.width || 0,
                    height: thumb.height || 0
                };
            }
        });
    }

    return {
        id: nocoAttachment.id || `att${Date.now()}${Math.random().toString(36).substring(2, 11)}`,
        width: nocoAttachment.width || 0,
        height: nocoAttachment.height || 0,
        url: url,
        filename: nocoAttachment.title || nocoAttachment.filename || 'unknown',
        size: nocoAttachment.size || 0,
        type: nocoAttachment.mimetype || nocoAttachment.type || 'unknown',
        thumbnails: thumbnails
    };
}

/**
 * Transform array of NocoDB attachments to Airtable format
 */
function transformAttachments(nocoAttachments) {
    if (!nocoAttachments) return [];

    // Handle both array and string JSON
    let attachments = nocoAttachments;
    if (typeof attachments === 'string') {
        try {
            attachments = JSON.parse(attachments);
        } catch (e) {
            console.warn('Error parsing JSON attachments:', e);
            return [];
        }
    }

    if (!Array.isArray(attachments)) {
        attachments = [attachments];
    }

    return attachments
        .map(transformAttachment)
        .filter(Boolean);
}

/**
 * Transform NocoDB relationship to Airtable format
 */
function transformRelation(nocoRelation) {
    if (!nocoRelation) return [];

    // If it's a number (foreign key), convert to Airtable format
    if (typeof nocoRelation === 'number') {
        return [`rec${nocoRelation}`];
    }

    // If it's a string, try to parse it
    if (typeof nocoRelation === 'string') {
        try {
            const parsed = JSON.parse(nocoRelation);
            return transformRelation(parsed);
        } catch (e) {
            // If not JSON, might be a single ID
            return [`rec${nocoRelation}`];
        }
    }

    // If it's an array of objects with Id
    if (Array.isArray(nocoRelation)) {
        return nocoRelation.map(item => {
            if (typeof item === 'object' && item.Id) {
                return `rec${item.Id}`;
            }
            return `rec${item}`;
        });
    }

    return [];
}

/**
 * Transform NocoDB date to Airtable format
 */
function transformDate(nocoDate) {
    if (!nocoDate) return null;

    // NocoDB dates are usually in ISO format, which Airtable also uses
    // Just ensure it's a proper ISO string
    try {
        const date = new Date(nocoDate);
        return date.toISOString();
    } catch (e) {
        console.warn('Invalid date format:', nocoDate);
        return nocoDate; // Return as-is if can't parse
    }
}

/**
 * Transform a field value based on field name and type
 */
function transformField(fieldName, value) {
    if (value === null || value === undefined) {
        return null;
    }

    // Handle empty arrays or arrays with null values
    if (Array.isArray(value)) {
        // Filter out null values from arrays
        const filteredValue = value.filter(item => item !== null && item !== undefined);
        if (filteredValue.length === 0) {
            return [];
        }
        // Update value to the filtered array for further processing
        value = filteredValue;
    }

    // Handle attachment fields - these need URL transformation
    if (fieldName === 'covers' || fieldName === 'flyer' || fieldName === 'other_events' ||
        fieldName === 'og_image' || fieldName === 'images' || fieldName === 'gallery' ||
        fieldName.includes('image') || fieldName.includes('attachment')) {
        return transformAttachments(value);
    }

    // Handle relationship fields - but be more selective
    // Only transform fields that are explicitly relationship fields, not simple numeric fields
    if (fieldName.includes('_link') || fieldName.includes('_relation') || fieldName.startsWith('_nc_m2m_')) {
        return transformRelation(value);
    }

    // Handle date fields
    if (fieldName.includes('date') || fieldName.includes('Date') || fieldName === 'CreatedAt' || fieldName === 'UpdatedAt') {
        return transformDate(value);
    }

    // Handle arrays of objects with Id (relationships) - but only if they're actually relationship objects
    if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' &&
        value[0] !== null && value[0].hasOwnProperty('Id') && Object.keys(value[0]).length > 1) {
        return transformRelation(value);
    }

    // Handle special array cases
    if (Array.isArray(value) && value.length === 1 && value[0] === null) {
        return [null];
    }
    if (Array.isArray(value) && value.length === 1 && value[0] === "\n") {
        return [null];
    }

    // Special handling for language fields in vocabulary table
    // Preserve language fields (en, es, it, de, ru, uk, fr) as-is
    if (fieldName === 'en' || fieldName === 'es' || fieldName === 'it' ||
        fieldName === 'de' || fieldName === 'ru' || fieldName === 'uk' || fieldName === 'fr') {
        return value;
    }

    // Special handling for dances field - convert comma-separated string to array
    if (fieldName === 'dances' && typeof value === 'string') {
        return value.split(',').map(dance => dance.trim()).filter(dance => dance.length > 0);
    }

    // Handle string fields - ensure they're not null/undefined to prevent limax errors
    if (typeof value === 'string' || (value !== null && value !== undefined)) {
        return value;
    }

    // For simple numeric fields like 'location', keep them as-is
    // Don't transform simple numbers to relationship arrays
    return value;
}

/**
 * Transform complete NocoDB record to Airtable format
 */
function transformRecord(nocoRecord) {
    if (!nocoRecord || typeof nocoRecord !== 'object') {
        return null;
    }

    // Use ncRecordId if present (maintains original Airtable ID)
    // Otherwise use Id with "rec" prefix
    const recordId = nocoRecord.ncRecordId || `rec${nocoRecord.Id || nocoRecord.id || Date.now()}`;

    // Fields to exclude from transformation
    const excludeFields = [
        'Id', 'id', 'CreatedAt', 'UpdatedAt', 'ncRecordId', 'ncRecordHash'
    ];

    // Transform all fields
    const fields = {};
    Object.entries(nocoRecord).forEach(([key, value]) => {
        if (!excludeFields.includes(key)) {
            try {
                fields[key] = transformField(key, value);
            } catch (error) {
                console.warn(`Error transforming field "${key}":`, error);
                // Keep the original value if transformation fails
                fields[key] = value;
            }
        }
    });

    return {
        id: recordId,
        fields: fields,
        get: function (fieldName) {
            return this.fields[fieldName];
        }
    };
}

function mergeEvents(record) {
    const otherEvents = record.get('other_events') || [];
    const fixedEvents = record.get('fixed_events') || [];
    return [...otherEvents, ...fixedEvents];
}

async function createOgImage(record) {
    const mergedEvents = mergeEvents(record);
    const eventCount = mergedEvents.length;
    const textComputed = eventCount > 1 ? `${eventCount} eventos` : eventCount === 1 ? "1 evento" : "No hay eventos";

    const imageBuffer = await createSocialImageWithText(textComputed);
    const imageUrl = await uploadImageAndGetUrl(imageBuffer);

    return {
        id: record.id,
        fields: {
            'og_image': [{ url: imageUrl, filename: `event-${record.id}.png` }]
        }
    };
}

async function updateOgImages(tableId) {
    // Fetch ALL records using pagination
    let allRecords = [];
    let offset = 0;
    const limit = 100;
    let hasMoreRecords = true;

    while (hasMoreRecords) {
        const url = `${NOCODB_BASE_URL}api/v2/tables/${tableId}/records?fields=other_events,fixed_events&limit=${limit}&offset=${offset}`;
        const response = await fetch(url, {
            headers: {
                'xc-token': NOCODB_API_TOKEN,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch records for OG image update: ${response.status}`);
        }

        const data = await response.json();
        const records = data.list || data;

        if (records && records.length > 0) {
            allRecords = allRecords.concat(records);
            offset += limit;
            hasMoreRecords = records.length === limit;
        } else {
            hasMoreRecords = false;
        }
    }

    // Transform records to have the proper structure with get() method
    const transformedRecords = allRecords.map(transformRecord).filter(Boolean);
    const updates = await Promise.all(transformedRecords.map(createOgImage));

    // Update records in batches of 10
    for (let i = 0; i < updates.length; i += 10) {
        const batch = updates.slice(i, i + 10);
        for (const update of batch) {
            const updateUrl = `${NOCODB_BASE_URL}api/v2/tables/${tableId}/records/${update.id}`;
            await fetch(updateUrl, {
                method: 'PATCH',
                headers: {
                    'xc-token': NOCODB_API_TOKEN,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(update.fields)
            });
        }
    }

    console.log(`OG_IMAGES UPDATED SUCCESSFULLY for table ${tableId}`);
}

async function bootstrapOgImages(tableLabels) {
    const tables = await fetchTableMetadata();

    for (const label of tableLabels) {
        const tableId = getTableIdByLabel(tables, label);
        if (tableId) {
            await updateOgImages(tableId);
        }
    }
}

async function fetchDataFromTable(tableLabel) {
    const tableLabelSanitized = tableLabel.toLowerCase();
    if (!TABLE_NAMES.includes(tableLabelSanitized)) return null;

    try {
        const tables = await fetchTableMetadata();
        const tableId = getTableIdByLabel(tables, tableLabel);

        if (!tableId) {
            console.warn(`Table with label "${tableLabel}" not found`);
            return { table: tableLabelSanitized, data: [] };
        }

        // Fetch ALL records using pagination
        let allRecords = [];
        let offset = 0;
        const limit = 100; // Fetch 100 records per request for efficiency
        let hasMoreRecords = true;

        while (hasMoreRecords) {
            const url = `${NOCODB_BASE_URL}api/v2/tables/${tableId}/records?limit=${limit}&offset=${offset}`;
            const response = await fetch(url, {
                headers: {
                    'xc-token': NOCODB_API_TOKEN,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch data from table ${tableLabel}: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const records = data.list || data;

            if (records && records.length > 0) {
                allRecords = allRecords.concat(records);
                offset += limit;

                // Check if we got fewer records than requested, indicating we've reached the end
                hasMoreRecords = records.length === limit;
            } else {
                hasMoreRecords = false;
            }
        }

        console.log(`Fetched ${allRecords.length} records from table ${tableLabel}`);

        // Transform NocoDB records to match Airtable structure
        const transformedRecords = allRecords.map(transformRecord).filter(Boolean);

        return { table: tableLabelSanitized, data: transformedRecords };
    } catch (error) {
        console.error(`Error fetching data from table ${tableLabel}:`, error);
        return { table: tableLabelSanitized, data: [] };
    }
}

export async function fetchTablesFromNocoDB() {
    return Promise.all(TABLE_NAMES.map(fetchDataFromTable));
}

const normalizeRecord = (record) => {
    // Record is already transformed to Airtable format with id, fields, and get() method
    const mergedEvents = mergeEvents(record);
    return {
        id: record.id,
        ...record.fields,
        other_events: mergedEvents  // Replace other_events with merged events
    };
};

// Simple approach with timeout protection
async function initializeWithTimeout() {
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Initialization timeout')), 10000); // 10 second timeout
    });

    const initPromise = (async () => {
        if (process.env.NODE_ENV === 'production') {
            bootstrapOgImages(['dancing_agenda', 'social_agenda']);
            console.log(`${process.env.NODE_ENV} Environment`);
        }

        const tables = await fetchTablesFromNocoDB();
        const getTable = (tableName) => tables.find((t) => t.table === tableName)?.data || [];

        const servicesRaw = getTable('services').map(normalizeRecord).filter(s => s.published);
        const processedServices = await Promise.all(servicesRaw.map(async (s) => {
            try {
                if (s.covers && s.covers[0]?.url) {
                    return {
                        ...s,
                        cover: await getImage({ src: s.covers[0].url, format: 'webp', width: 1080, height: 1080 })
                    };
                }
                return s;
            } catch (error) {
                console.warn('Error processing service image:', error);
                return s;
            }
        }));

        return {
            latinEvents: getTable('dancing_agenda').map(normalizeRecord),
            socialEvents: getTable('social_agenda').map(normalizeRecord),
            reviews: getTable('reviews').map(normalizeRecord),
            vocabulary: getTable('vocabulary').map(normalizeRecord),
            bio: getTable('bio').map(normalizeRecord),
            settings: getTable('settings').map(normalizeRecord),
            properties: getTable('housing').map(normalizeRecord),
            services: processedServices
        };
    })();

    return Promise.race([initPromise, timeoutPromise]);
}

let data;
try {
    data = await initializeWithTimeout();
    console.log('NocoDB data loaded successfully');
} catch (error) {
    console.error('Failed to load NocoDB data:', error);
    data = {
        latinEvents: [],
        socialEvents: [],
        reviews: [],
        vocabulary: [],
        bio: [],
        settings: [],
        properties: [],
        services: []
    };
}

export const latinEvents = data.latinEvents;
export const socialEvents = data.socialEvents;
export const reviews = data.reviews;
export const vocabulary = data.vocabulary;
export const bio = data.bio;
export const settings = data.settings;
export const properties = data.properties;
export const services = data.services;
