---
import { settings } from '~/connectors/nocodb';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

export interface Props {
  allowedRoutes?: string[];
  exactRoutes?: string[];
  hideDays?: number;
  position?:
    | 'top-left'
    | 'top-center'
    | 'top-right'
    | 'center-left'
    | 'center'
    | 'center-right'
    | 'bottom-left'
    | 'bottom-center'
    | 'bottom-right';
  imageUrl?: string;
  altText?: string;
  linkUrl?: string;
  openInNewTab?: boolean;
  countdownEndDate?: string;
  countdownText?: string;
  fullscreen?: boolean;
}

const {
  allowedRoutes = [],
  exactRoutes = [],
  hideDays = 7,
  position = 'bottom-right',
  imageUrl,
  altText,
  linkUrl,
  openInNewTab = false,
  countdownEndDate,
  countdownText,
  fullscreen = false,
} = Astro.props;

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const { 'popup.isEnabled': popupIsEnabled } = Object.fromEntries(settings.map((s) => [s.key, s.value]));

const currentPath = Astro.url.pathname;
const isAllowedRoute = allowedRoutes.some(
  (route) => currentPath === route || (route !== '/' && currentPath.startsWith(route))
);
const isExactRoute = exactRoutes.includes(currentPath);
const shouldShow = popupIsEnabled && (isAllowedRoute || isExactRoute);

const positionClasses = {
  'top-left': 'top-2 left-2',
  'top-center': 'top-2 left-1/2 -translate-x-1/2',
  'top-right': 'top-2 right-2',
  'center-left': 'top-1/2 left-2 -translate-y-1/2',
  center: 'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
  'center-right': 'top-1/2 right-2 -translate-y-1/2',
  'bottom-left': 'bottom-2 left-2',
  'bottom-center': 'bottom-2 left-1/2 -translate-x-1/2',
  'bottom-right': 'bottom-2 right-2',
};

const popupClasses = `fixed ${fullscreen ? 'inset-0 w-full h-full max-w-none max-h-none rounded-none' : `${positionClasses[position]} w-[70vw] sm:w-[50vw] md:w-[30vw] lg:w-[20vw] max-h-[80vh] rounded-lg`} z-50 shadow-lg flex flex-col overflow-hidden transition-all duration-300 ease-in-out backdrop-blur-md transform translate-y-0 opacity-100`;

// Array di colori e gradienti radiali
const backgroundStyles = [
  'bg-blue-500',
  'bg-green-500',
  'bg-red-500',
  'bg-purple-500',
  'bg-yellow-500',
  'bg-gradient-to-r from-cyan-500 to-blue-500',
  'bg-gradient-to-r from-purple-500 to-pink-500',
  'bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-blue-700 via-blue-800 to-gray-900',
  'bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-pink-500 via-red-500 to-yellow-500',
  'bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-700 via-gray-900 to-black',
];
---

{
  shouldShow && (
    <div id="popup" class={popupClasses}>
      <button
        id="closeButton"
        class="absolute top-1 right-1 bg-black bg-opacity-60 hover:bg-opacity-80 text-white rounded-full w-6 h-6 flex items-center justify-center cursor-pointer transition-colors duration-200 z-[51] text-xs"
        aria-label="close popup"
      >
        ✕
      </button>
      <div class="flex flex-col items-center justify-center p-2 sm:p-3 overflow-y-auto h-full">
        {countdownEndDate && (
          <div
            id="countdown"
            class="w-full px-4 py-2 rounded-xl bg-gradient-to-br from-red-900 to-red-400 bg-opacity-80 backdrop-blur-lg text-white"
          >
            <p class="text-sm sm:text-base font-semibold text-justify p-2">{t(countdownText || 'countdown.text')}</p>
            <div id="countdown-timer" class="flex justify-center space-x-1 sm:space-x-2 mt-2">
              {['days', 'hours', 'minutes', 'seconds'].map((unit) => (
                <div class="flex flex-col items-center">
                  <span
                    id={unit}
                    class="bg-white bg-opacity-20 font-bold text-white text-lg sm:text-xl rounded px-1 sm:px-2 py-0.5 min-w-[2rem] sm:min-w-[2.5rem]"
                  >
                    00
                  </span>
                  <span class="text-[10px] sm:text-xs mt-0.5">{t(unit).substr(0, 3)}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        <slot />
        {imageUrl &&
          (linkUrl ? (
            <a
              href={linkUrl}
              target={openInNewTab ? '_blank' : '_self'}
              rel={openInNewTab ? 'noopener noreferrer' : ''}
              class="mt-2 w-full"
            >
              <img src={imageUrl} alt={altText} class="w-full h-auto object-contain rounded-lg max-h-[40vh]" />
            </a>
          ) : (
            <img src={imageUrl} alt={altText} class="w-full h-auto object-contain rounded-lg mt-2 max-h-[40vh]" />
          ))}
      </div>
    </div>
  )
}

<style>
  #popup {
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
</style>

<script define:vars={{ hideDays, countdownEndDate, fullscreen, backgroundStyles }}>
  let popup, closeButton;

  function initializePopup() {
    popup = document.getElementById('popup');
    closeButton = document.getElementById('closeButton');

    if (!popup) return; // Exit if popup doesn't exist

    if (fullscreen) {
      document.body.classList.add('fullscreen');
    }

    // Applica uno sfondo casuale
    const randomBackground = backgroundStyles[Math.floor(Math.random() * backgroundStyles.length)];
    popup.classList.add(...randomBackground.split(' '));

    const STORAGE_KEY = 'popupLastClosed';

    const storage = {
      set: (key, value) => {
        try {
          localStorage.setItem(key, value);
        } catch (e) {
          document.cookie = `${key}=${value};max-age=${60 * 60 * 24 * hideDays};path=/`;
        }
      },
      get: (key) => {
        try {
          return localStorage.getItem(key);
        } catch (e) {
          const match = document.cookie.match(new RegExp('(^| )' + key + '=([^;]+)'));
          return match ? match[2] : null;
        }
      },
    };

    const shouldShowPopup = () => {
      const lastClosed = storage.get(STORAGE_KEY);
      if (!lastClosed) return true;
      const lastClosedDate = new Date(parseInt(lastClosed));
      const now = new Date();
      return (now - lastClosedDate) / (1000 * 60 * 60 * 24) >= hideDays;
    };

    if (shouldShowPopup()) {
      popup.classList.remove('opacity-0', 'invisible');
      popup.classList.add('opacity-100', 'visible');
    } else {
      popup.classList.add('opacity-0', 'invisible');
      popup.classList.remove('opacity-100', 'visible');
    }

    closeButton?.addEventListener('click', () => {
      popup.classList.remove('opacity-100', 'visible');
      popup.classList.add('opacity-0', 'invisible');
      storage.set(STORAGE_KEY, Date.now().toString());
    });

    if (countdownEndDate) {
      const updateCountdown = () => {
        if (!document.body.contains(popup)) {
          // Stop the countdown if the popup is no longer in the DOM
          clearInterval(countdownInterval);
          return;
        }

        const now = new Date().getTime();
        const endTime = new Date(countdownEndDate).getTime();
        const timeLeft = endTime - now;

        if (timeLeft > 0) {
          const units = ['days', 'hours', 'minutes', 'seconds'];
          const values = [
            Math.floor(timeLeft / (1000 * 60 * 60 * 24)),
            Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
            Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60)),
            Math.floor((timeLeft % (1000 * 60)) / 1000),
          ];

          units.forEach((unit, index) => {
            const element = document.getElementById(unit);
            if (element) {
              element.textContent = values[index].toString().padStart(2, '0');
            }
          });
        } else {
          const countdownTimer = document.getElementById('countdown-timer');
          if (countdownTimer) {
            countdownTimer.innerHTML = "<p class='text-xl font-bold'>Expired!</p>";
          }
        }
      };

      updateCountdown();
      const countdownInterval = setInterval(updateCountdown, 1000);
    }
  }

  // Run the initialization when the DOM is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePopup);
  } else {
    initializePopup();
  }
</script>
