---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import type { Widget } from '~/types';
import Button from '~/components/ui/Button.astro';
import ServiceCard from './ServiceCard.astro';
import { services } from '~/connectors/nocodb';
import { getLangFromUrl, useTranslations } from '~/i18n/utils';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

export interface Props extends Widget {
  title?: string;
  linkText?: string;
  linkUrl?: string | URL;
  serviceToExclude?: string;
  information?: string;
  count?: number;
  addScript?: boolean;
  flyersPreview?: boolean;
}

const {
  title = await Astro.slots.render('title'),
  linkText,
  linkUrl,
  information = await Astro.slots.render('information'),
  count = 4,
  addScript = false,
  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
  serviceToExclude,
} = Astro.props;

const servicesComputed = !serviceToExclude
  ? services
  : services.filter((service) => service.name_t !== serviceToExclude);

const hasServices = !!servicesComputed.length;

const getGridCols = (length: number) => {
  if (length === 1) return 'lg:grid-cols-1';
  if (length === 2) return 'lg:grid-cols-2';
  return 'lg:grid-cols-3';
};
---

{
  hasServices && (
    <WidgetWrapper id={id} isDark={isDark} containerClass={classes?.container} bg={bg}>
      <div class="flex flex-col lg:justify-between lg:flex-row mb-8">
        {title && (
          <div class="md:max-w-sm">
            <h2
              class="text-3xl font-bold tracking-tight sm:text-4xl sm:leading-none group font-heading mb-2"
              set:html={title}
            />
            {linkText && (
              <Button variant="link" href={linkUrl || ''}>
                {linkText}
              </Button>
            )}
          </div>
        )}

        {information && <p class="text-muted dark:text-slate-400 lg:text-sm lg:max-w-md" set:html={information} />}
      </div>

      <div id="service-slider" class="swiper">
        <div
          class={`swiper-wrapper my-4 grid gap-6 grid-cols-1 md:grid-cols-2 ${getGridCols(servicesComputed.length)}`}
        >
          {servicesComputed.map((s) => (
            <div class="swiper-slide">
              <ServiceCard
                name={t(`services.${s.name_t}`)}
                cover={s.cover}
                emoji={s.emoji}
                slug={t(`services.${s.name_t}.slug`)}
                shortDescription={t(`services.${s.name_t}.description`)}
              />
            </div>
          ))}
        </div>

        {servicesComputed.length > 3 && (
          <>
            <div class="swiper-button-prev" />
            <div class="swiper-button-next" />
          </>
        )}
      </div>
    </WidgetWrapper>
  )
}

{
  addScript && (
    <>
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
      <script is:inline src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js" />
    </>
  )
}

<script is:inline>
  const serviceSwiper = new Swiper('#service-slider', {
    preventClicks: true,
    autoplay: { delay: 5000 },
    autoHeight: true,
    slidesPerView: 'auto',
    spaceBetween: 20,
    breakpoints: {
      320: {
        slidesPerView: 1,
        spaceBetween: 20,
      },
      768: {
        slidesPerView: 2,
        spaceBetween: 30,
      },
      1024: {
        slidesPerView: 3,
        spaceBetween: 40,
      },
    },
    direction: 'horizontal',
    loop: true,
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
  });
</script>
